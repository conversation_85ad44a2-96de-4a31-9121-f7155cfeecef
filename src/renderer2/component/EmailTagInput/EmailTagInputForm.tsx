import React, { useState, useRef, useEffect } from 'react';
import { UseFormRegisterReturn, useWatch } from 'react-hook-form';
import clsx from 'clsx';
import styles from './EmailTagInput.module.scss';

export interface EmailTagInputFieldProps {
  value?: string[];
  onChange?: (emails: string[]) => void;
  placeholder?: string;
  maxEmails?: number;
  disabled?: boolean;
  register?: UseFormRegisterReturn;
  error?: string;
  onBlur?: () => void;
  onFocus?: () => void;
  control?: any; // Add control prop for useWatch
  inputBlur?: () => void;
}

const EmailTagInputField = ({
  value = [],
  onChange,
  placeholder = "Enter email addresses...",
  maxEmails,
  disabled = false,
  register,
  error,
  onBlur,
  onFocus,
  control, 
  inputBlur
}: EmailTagInputFieldProps) => {
  const [emailTags, setEmailTags] = useState<string[]>(value);
  const [emailInput, setEmailInput] = useState('');
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingValue, setEditingValue] = useState('');
  const emailInputRef = useRef<HTMLInputElement>(null);
  const editingInputRef = useRef<HTMLInputElement>(null);

  // Watch the form value if control is provided
  const watchedValue = control && register?.name ? useWatch({ control, name: register.name }) : '';

  // Update internal state when value prop changes or watched value changes
  useEffect(() => {
    if (watchedValue && typeof watchedValue === 'string') {
      const allEmails = watchedValue.split(',').filter(Boolean);
      // Filter out invalid emails when setting tags
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const validEmails = allEmails.filter(email => emailRegex.test(email));
      setEmailTags(validEmails);
    } else if (value.length > 0) {
      setEmailTags(value);
    }
  }, [value, watchedValue]);

  // Focus editing input when editing starts
  useEffect(() => {
    if (editingIndex !== null && editingInputRef.current) {
      editingInputRef.current.focus();
      editingInputRef.current.select();
    }
  }, [editingIndex]);

  const addEmailTag = (email: string) => {
    const emailTrimmed = email.trim();
    
    if (!emailTrimmed) {
      return;
    }

    // Check max emails limit
    if (maxEmails && emailTags.length >= maxEmails) {
      return;
    }

    // Validate email format - simplified and more reliable regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    const isValidEmail = emailRegex.test(emailTrimmed);
    
    if (isValidEmail) {
      // Only add valid emails as tags
      const newTags = [...emailTags, emailTrimmed];
      setEmailTags(newTags);
      setEmailInput('');
      // Pass all emails (valid + invalid) to form for validation
      onChange?.(newTags);
    } else {
      // For invalid emails, don't add as tag, keep in input, but still pass to form for validation
      const allEmails = [...emailTags, emailTrimmed];
      // Don't clear the input - keep the invalid email text visible
      onChange?.(allEmails);
    }
  };

  const removeEmailTag = (indexToRemove: number) => {
    const newTags = emailTags.filter((_, index) => index !== indexToRemove);
    setEmailTags(newTags);
    onChange?.(newTags);
  };

  const startEditing = (index: number) => {
    if (disabled) return;
    setEditingIndex(index);
    setEditingValue(emailTags[index]);
  };

  const saveEdit = () => {
    if (editingIndex === null) return;

    const emailTrimmed = editingValue.trim();
    
    if (!emailTrimmed) {
      // If empty, remove the tag
      removeEmailTag(editingIndex);
    } else {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const isValidEmail = emailRegex.test(emailTrimmed);
      
      if (isValidEmail) {
        // Update the tag with the new email
        const newTags = [...emailTags];
        newTags[editingIndex] = emailTrimmed;
        setEmailTags(newTags);
        onChange?.(newTags);
      }
      // If invalid, keep the original value
    }
    
    setEditingIndex(null);
    setEditingValue('');
  };

  const cancelEdit = () => {
    setEditingIndex(null);
    setEditingValue('');
  };

  const handleEditKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      saveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEdit();
    }
  };

  const handleEmailKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addEmailTag(emailInput);
    } else if (e.key === 'Backspace' && emailInput === '' && emailTags.length > 0) {
      e.preventDefault();
      const newTags = [...emailTags];
      const removedTag = newTags.pop();
      setEmailTags(newTags);
      onChange?.(newTags);
      setEmailInput(removedTag || '');
    }
  };

  const handleInputBlur = () => {
    if (emailInput) {
      addEmailTag(emailInput);
    }
    onBlur?.();
  };

  const handleEditBlur = () => {
    saveEdit();
    inputBlur?.();
  };

  return (
    <>
      <div className={clsx(styles.emailInputContainer)}>
        <div className={clsx(
          styles.emailTagsContainer, 
          emailInput === '' && styles.emptyInput,
          error && styles.inputError,
        )}>
          {emailTags.map((tag, index) => (
            <div 
              key={index} 
              className={clsx(
                styles.emailTag,
                editingIndex === index && styles.focused
              )}
              onClick={() => startEditing(index)}
            >
              {editingIndex === index ? (
                <input
                  ref={editingInputRef}
                  type="text"
                  value={editingValue}
                  onChange={(e) => setEditingValue(e.target.value)}
                  onKeyDown={handleEditKeyPress}
                  onBlur={handleEditBlur}
                  className={styles.editInput}
                  disabled={disabled}
                  onFocus={onFocus}
                />
              ) : (
                <>
                  <span className={styles.emailText}>{tag}</span>
                  <span 
                    className={styles.removeTag} 
                    onClick={(e) => {
                      e.stopPropagation();
                      removeEmailTag(index);
                    }}
                  >
                    X
                  </span>
                </>
              )}
            </div>
          ))}
          <input
            ref={emailInputRef}
            type="text"
            value={emailInput}
            onChange={(e) => setEmailInput(e.target.value)}
            onKeyDown={handleEmailKeyPress}
            onBlur={handleInputBlur}
            onFocus={onFocus}
            className={clsx(styles.emailInput)}
            placeholder={emailTags.length === 0 ? placeholder : ""}
            disabled={disabled}
            name={register?.name}
          />
        </div>
        
        {/* Show errors */}
       
      </div>

      {/* Hidden input to register the field with react-hook-form */}
      {register && (
        <input 
          type="hidden" 
          {...register} 
          value={[...emailTags, emailInput.trim()].filter(Boolean).join(',')}
        />
      )}
    </>
  );
};

export default EmailTagInputField; 