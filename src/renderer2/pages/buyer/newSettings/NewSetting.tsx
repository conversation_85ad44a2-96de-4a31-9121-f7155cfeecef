import React, { useEffect, useRef, useState } from 'react';
import styles from './NewSetting.module.scss';
import SearchHeader from '../../SearchHeader';
import TabNavigation from './tabs/TabNavigation';
import CompanyTab from './tabs/CompanyTab';
import UserTab from './tabs/UserTab';
import ShipmentsTab from './tabs/ShipmentsTab';
import PaymentsTab from './tabs/PaymentsTab';
import NotificationsTab from './tabs/NotificationsTab';
import axios from 'axios';
import { formatCurrency, useGlobalStore } from '@bryzos/giss-ui-library';
import useGetBuyingPreference from 'src/renderer2/hooks/useGetBuyingPreference';
import { yupResolver } from '@hookform/resolvers/yup';
import { set, useForm } from 'react-hook-form';
import { settingSchema } from './schemas';
import {
  formatPhoneNumberRemovingCountryCode,
  formatPhoneNumberWithHyphen,
} from 'src/renderer2/helper';
import {
  defaultResaleCertificateLine,
  RecevingHoursFrom,
  RecevingHoursTo,
} from 'src/renderer2/common';
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import useGetDeliveryAddress from 'src/renderer2/hooks/useGetDeliveryAddress';
import useGetCompanyLists from 'src/renderer2/hooks/useGetCompanyLists';
import { useLocation } from 'react-router-dom';
import DropdownSave from './components/DropdownSave';
import ShipmentListing from './tabs/ShipmentListing/ShipmentListing';
import ResaleCertTab from './tabs/ResaleCertTab';
import SubscribeTab from '../../Subscribe/SubscribeTab';

const options = {
  fonts: [
    {
      cssSrc:
        'https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap',
    },
  ],
};

const NewSetting: React.FC<{
  mainWrapperRef: React.RefObject<HTMLDivElement>;
  routerContainerRef: React.RefObject<HTMLDivElement>;
}> = ({ mainWrapperRef, routerContainerRef }) => {
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid },
    getFieldState,
  } = useForm({
    resolver: yupResolver(settingSchema),
    mode: 'onBlur',
  });

  const [activeTab, setActiveTab] = useState<string>('COMPANY');
  const { userData, showLoader, setShowLoader, referenceData , bryzosPayApprovalStatus ,navigationStateForNotification , setNavigationStateForNotification }: any =
    useGlobalStore();
  const [stripePromise] = useState(
    loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY)
  );
  const [isBnplFilled, setIsBnplFilled] = useState<boolean>(false);
  const [isBnplApproved, setIsBnplApproved] = useState<string>('');
  const [bnplCreditStatus, setBnplCreditStatus] = useState<string>('');
  const [bnplStatus, setBnplStatus] = useState<string>('');
  const [creditStatus, setCreditStatus] = useState<string>('');
  const [deliveryAddresses, setDeliveryAddresses] = useState<any>([]);
  const [yourCompanyList, setYourCompanyList] = useState([]);
  const location = useLocation();
  const shipmentListingRef = useRef(null);
  
  const [saveFunctions, setSaveFunctions] = useState<{
    onSave: (() => void) | null;
    onSaveAndNext: (() => void) | null;
    onSaveAndExit: (() => void) | null;
    isDisabled: boolean;
  }>({
    onSave: null,
    onSaveAndNext: null,
    onSaveAndExit: null,
    isDisabled: true,
  });

  const {
    data: buyingPreferenceData,
    isLoading: isBuyingPreferenceDataLoading,
    isFetching: isBuyingPreferenceDataFetching,
  } = useGetBuyingPreference();

  const {
    data: deliveryAddressData,
    isLoading: isDeliveryAddressLoading,
    isFetching: isDeliveryAddressFetching,
  } = useGetDeliveryAddress();

  const { data: companyListsData, isLoading: isCompanyListsDataLoading } =
    useGetCompanyLists();


    useEffect(() => {
      if(location?.state?.from === 'createPo'){
        setActiveTab('PAYMENTS');
      }
    }, [location.state?.from]);

  useEffect(() => {
    if(navigationStateForNotification?.tab){
      setActiveTab(navigationStateForNotification?.tab);
      setNavigationStateForNotification(null);
    }
  }, [navigationStateForNotification?.tab]);

  useEffect(() => {
    if (
      buyingPreferenceData &&
      !isBuyingPreferenceDataFetching &&
      !isBuyingPreferenceDataLoading
    ) {
      // handleSetInitialValues(buyingPreferenceData);
    setShowLoader(false);

    }
  }, [
    buyingPreferenceData,
    isBuyingPreferenceDataFetching,
    isBuyingPreferenceDataLoading,
  ]);

  useEffect(() => {
    if (
      deliveryAddressData &&
      !isDeliveryAddressFetching &&
      !isDeliveryAddressLoading
    ) {
      if (referenceData?.ref_states) {
        const modifiedRes = deliveryAddressData.map((address: any) => {
          address.stateCode = referenceData?.ref_states?.find(
            (state: any) => state.id === address.state_id
          )?.code;
          return address;
        });
        setDeliveryAddresses(modifiedRes);
      }
    }
  }, [
    deliveryAddressData,
    isDeliveryAddressFetching,
    isDeliveryAddressLoading,
  ]);

  useEffect(() => {
    if (watch('parentCompanyName') !== '' && companyListsData) {
      const companyData = companyListsData?.find(
        (companyData: any) =>
          companyData.company_name === watch('parentCompanyName')
      );
      setYourCompanyList(companyData?.client_company ?? []);
    }
  }, [watch('parentCompanyName'), companyListsData]);


  const renderTabContent = () => {
    switch (activeTab) {
      case 'COMPANY':
        return (
          <CompanyTab
            yourCompanyList={yourCompanyList}
            setActiveTab={setActiveTab}
            setSaveFunctions={setSaveFunctions}
          />
        );
      case 'USER':
        return (
          <UserTab
            setActiveTab={setActiveTab}
            setSaveFunctions={setSaveFunctions}
            routerContainerRef={routerContainerRef}
          />
        );
      case 'SHIPMENTS':
        return (
          <ShipmentListing shipmentListingRef={shipmentListingRef} />
        );
      case 'RESALE_CERT':
        return (
          <ResaleCertTab/>
        );
      case 'PAYMENTS':
        return (
          <Elements stripe={stripePromise} options={options}>
            <PaymentsTab
            />
          </Elements>
        );
      case 'NOTIFICATIONS':
        return (
          <NotificationsTab
            setActiveTab={setActiveTab}
            setSaveFunctions={setSaveFunctions}
          />
        );
      case 'SUBSCRIPTION':
        return (
          <Elements stripe={stripePromise} options={options}>
            <SubscribeTab 
              setActiveTab={setActiveTab}
              setSaveFunctions={setSaveFunctions}
            />
          </Elements>
        );
      default:
        return (
          <CompanyTab
          yourCompanyList={yourCompanyList}
          setActiveTab={setActiveTab}
          setSaveFunctions={setSaveFunctions}
        />
        );
    }
  };

  return (
    <div className={styles.newSetting} ref={shipmentListingRef}>
      
      <div className={styles.newSettingContent}>
      {Boolean(saveFunctions.onSave) && (
          <div className={styles.saveButtonContainer}>
            {
              activeTab !== 'SHIPMENTS' && activeTab !== 'RESALE_CERT' && activeTab !== 'PAYMENTS' && (
                <DropdownSave
                  onSave={saveFunctions.onSave}
                  onSaveAndNext={saveFunctions.onSaveAndNext || undefined}
                  onSaveAndExit={saveFunctions.onSaveAndExit || undefined}
                  isDisabled={saveFunctions.isDisabled}
                />
              )
            }
            
            <div className={styles.settingTitle}>settings</div>
          </div>
        )}
        <TabNavigation activeTab={activeTab} setActiveTab={setActiveTab} />
        {renderTabContent()}
      </div>
    </div>
  );
};

export default NewSetting;
