import React, { useEffect, useRef, useState } from 'react';
import styles from './TabContent.module.scss';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { companySchema, settingSchema } from '../schemas';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import clsx from 'clsx';
import { CustomMenu } from '../../CustomMenu';
import { watch } from 'fs';
import { Autocomplete, ClickAwayListener, Dialog, Select, TextField } from '@mui/material';
import { Watch } from '@mui/icons-material';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/close-icon.svg';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import CustomAddressComponent from '../components/CustomAddressComponent';
import axios from 'axios';
import { useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { useImmer } from 'use-immer';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import { useQueryClient } from '@tanstack/react-query';
import { prefixUrl, reactQueryKeys, routes } from 'src/renderer2/common';
import { navigatePage } from 'src/renderer2/helper';
import { ReactComponent as IconUpload } from '../../../../assets/New-images/icon-upload.svg';
import { v4 as uuidv4 } from 'uuid';
import DropdownSave from '../components/DropdownSave';
import { EmailTagInputField } from 'src/renderer2/component/EmailTagInput';
import SingleStateSelector from '../components/StateSelector/SingleStateSelector';

interface InputFocusState {
    parentCompanyName: boolean;
    companyDBAName: boolean;
    companyType: boolean;
    billingContactName: boolean;
    billingContactEmail: boolean;
    sendInvoicesTo: boolean;
    sendRemittancesTo: boolean;
    companyAddressLine1: boolean;
    companyAddressLine2: boolean;
    companyAddressCity: boolean;
    companyAddressState: boolean;
    companyAddressZip: boolean;
}

const CompanyTab: React.FC<{ yourCompanyList: any, setActiveTab: any, setSaveFunctions: any}> = ({ yourCompanyList, setActiveTab , setSaveFunctions }) => {
    const {
        register,
        handleSubmit,
        clearErrors,
        setError,
        setValue,
        reset,
        watch,
        control,
        getValues,
        trigger,
        resetField,
        formState: { errors, dirtyFields, isDirty, isValid, isSubmitting  },
        getFieldState,
      } = useForm({
        resolver: yupResolver(companySchema),
        mode: 'onBlur',
      });
    
    const selectCompanyHQAddressRef = useRef(null);
    const [isInputFocused, setIsInputFocused] = useState<InputFocusState>({
        parentCompanyName: false,
        companyDBAName: false,
        companyType: false,
        billingContactName: false,
        billingContactEmail: false,
        sendInvoicesTo: false,
        sendRemittancesTo: false,
        companyAddressLine1: false,
        companyAddressLine2: false,
        companyAddressCity: false,
        companyAddressState: false,
        companyAddressZip: false,
    });
    const {referenceData , setShowLoader, userData}: any = useGlobalStore();
    const [states, setStates] = useState<any[]>([]);
    const [yourCompanyValue, setYourCompanyValue] = useImmer(null);
    const [yourCompanyInput, setYourCompanyInput] = useState("");
    const { mutateAsync: saveUserSettings } = useSaveUserSettings();
    const { buyerSetting }: any = useBuyerSettingStore();
    const queryClient = useQueryClient();

    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const companyW9FormRef = useRef<HTMLInputElement>(null);
    const isButtonDisabled = !isValid || isSubmitting || !isDirty;
    const [isAddressContainerClicked, setIsAddressContainerClicked] = useState(false);
    const stateSelectorRef = useRef<HTMLDivElement>(null);
    const [isStateSelectorFocused, setIsStateSelectorFocused] = useState(false);
    const addressContainerRef = useRef<HTMLDivElement>(null);
    const line1InputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        if(buyerSetting) {
            setValue('parentCompanyName', buyerSetting?.company_name || '');
            setValue('companyType', buyerSetting?.company_type || '');
            setValue('companyAddress', {
              line1: buyerSetting?.company_address?.line1 || '',
              line2: buyerSetting?.company_address?.line2 || '',
              city: buyerSetting?.company_address?.city || '',
              state: buyerSetting?.company_address?.state_id || '',
              stateCode: buyerSetting?.company_address?.state_code || '',
              zip: buyerSetting?.company_address?.zip || '',
            });
            setValue('companyW9Form', {
                cerificate_url_s3: buyerSetting?.company_w9_pdf_url || '',
                file_name: buyerSetting?.company_w9_pdf_name || '',
            });
            setValue('billingContactName', buyerSetting?.ap_contact_name || '');
            setValue('billingContactEmail', buyerSetting?.ap_email_id || '');
            setValue('sendInvoicesTo', buyerSetting?.send_invoices_to || '');
            setValue('sendRemittancesTo', '<EMAIL>');
        }
    }, [buyerSetting]);


    useEffect(() => {
        setSaveFunctions({
            onSave: () => handleSubmit(handleSaveCompany)(),
            onSaveAndNext: () => handleSubmit(handleSaveAndNext)(),
            onSaveAndExit: () => handleSubmit(handleSaveAndExit)(),
            isDisabled: isButtonDisabled,
        });
    }, [isButtonDisabled, handleSubmit]);


    useEffect(() => {   
        setTimeout(() => {
            const parentCompanyNameInput = document.getElementById('parentCompanyName');
            if (parentCompanyNameInput) {
                parentCompanyNameInput.focus();
            }
        }, 100)
    }, []);


        useEffect(() => {
            handleStateZipValidation('companyAddress.zip', 'companyAddress.state')
        }, [watch('companyAddress.state'), watch('companyAddress.zip')])


    useEffect(() => {
        if(referenceData?.ref_states) {
            setStates(referenceData?.ref_states)
        }
    }, [referenceData])

    useEffect(() => {
        if (isAddressContainerClicked && line1InputRef.current) {
          // Small delay to ensure the input is rendered
          setTimeout(() => {
            line1InputRef.current?.focus();
          }, 0);
        }
      }, [isAddressContainerClicked]);


    const handleInputFocus = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: true,
        }));
    };

    const handleInputBlur = (inputName: keyof InputFocusState): void => {
        setIsInputFocused((prevState) => ({
            ...prevState,
            [inputName]: false,
        }));
    };

    const companyTypes = [
        { title: 'Fabricator', value: 'Fabricator' },
        { title: 'Constructor', value: 'Constructor' },
        { title: 'Distributor', value: 'Distributor' },
        { title: 'OEM', value: 'OEM' },
    ];

    const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
        try {
            if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
                const payload = {
                data: {
                    state_id: Number(getValues(stateCode)),
                    zip_code: parseInt(getValues(zipCode)),
                },
            };
            const checkStateZipResponse = await axios.post(
                import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
                payload
            );
            if (checkStateZipResponse.data.data === true) {
                clearErrors([stateCode, zipCode]);
                return true;
            } else {
                setError(stateCode, { message: "The zip code and state code do not match" });
                setError(zipCode, { message: "The zip code and state code do not match" });
                return false;
            }
        }
     } catch (error) {
            console.error(error)
        }
    };

    // Remove the useEffect that runs on every change
    // Instead, we'll handle validation in the onBlur events


    const handleSaveCompany = async (data: any) => {
        try{
            if(isValid){
                setShowLoader(true)
                const payload = {
                    company_name: data.parentCompanyName,
                    company_type: data.companyType,
                    company_address: {
                        line1: data.companyAddress.line1,
                        line2: data.companyAddress.line2?.trim() || null,
                        city: data.companyAddress.city,
                        state_id: Number(data.companyAddress.state),
                        zip: data.companyAddress.zip
                    },
                    company_w9_pdf_name: data.companyW9Form.file_name,
                    company_w9_pdf_url: data.companyW9Form.cerificate_url_s3,
                    ap_contact_name: data.billingContactName,
                    ap_email_id: data.billingContactEmail,
                    send_invoices_to: data.sendInvoicesTo,
                    send_remittances_to: data.sendRemittancesTo,
                }
                console.log('payload' , payload)
                await saveUserSettings({route: 'user/buyer/settings/company', data: payload})
                // queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
                reset(data); 
                setShowLoader(false)
            }
        }catch(err){
            console.error(err)
        }
    }

    const handleSaveAndNext = async (data: any) => {
        await handleSaveCompany(data);
        // Navigate to next tab or step
        setActiveTab && setActiveTab('USER'); // Assuming next tab index is 1
    }

    const handleSaveAndExit = async (data: any) => {
        await handleSaveCompany(data);
        navigatePage(location.pathname, { path: routes.homePage })
    }

    const handleDropdownToggle = () => {
        if (!isValid || isSubmitting || !isDirty) return;
        setIsDropdownOpen(!isDropdownOpen);
    }

    const handleClickAway = () => {
        setIsDropdownOpen(false);
    }

    const irsW9FormEditHandler = () => {
        companyW9FormRef?.current?.click();
    }

    const uploadW9Form = async (event: any) => {
        const file = event.target.files[0];
        setValue('companyW9Form.file_name', file.name , { shouldValidate: true })
        setValue('companyW9Form.file', file)

        if (event.target.files.length !== 0) {
            let index = file.name.length - 1;
            for (; index >= 0; index--) {
                if (file.name.charAt(index) === '.') {
                    break;
                }
            }
            const ext = file.name.substring(index + 1, file.name.length);

            const objectKey = import.meta.env.VITE_ENVIRONMENT + '/' + userData.data.id + '/' + prefixUrl.buyerCompanyW9Prefix + '-' + uuidv4() + '.' + ext;

            const payload = {
                data: {
                    "bucket_name": import.meta.env.VITE_S3_UPLOAD_SETTINGS_IRS_W9_BUCKET_NAME,
                    "object_key": objectKey,
                    "expire_time": 300

                }
            }
            let setIRSUrl = 'https://' + payload.data.bucket_name + ".s3.amazonaws.com/" + payload.data.object_key;
            axios.post(import.meta.env.VITE_API_SERVICE + '/user/get_signed_url', payload)
                .then(response => {
                    const signedUrl = response.data.data;
                    axios.put(signedUrl, file)
                        .then(response => {
                            if (response.status === 200) {
                                setValue('companyW9Form.cerificate_url_s3', setIRSUrl , { shouldValidate: true })
                            }
                        })
                        .catch(error => {
                            console.error(error);
                            setShowLoader(false);
                        }
                        );
                })
                .catch(error => {
                    console.error(error);
                    setShowLoader(false);
                }
                );

        }
    }

    const handleShipmentAddressContainerClickAway = () => {
        if (!isStateSelectorFocused) {
          if(!(errors?.companyAddress?.line1 || errors?.companyAddress?.line2 || errors?.companyAddress?.city || errors?.companyAddress?.state || errors?.companyAddress?.zip || errors?.companyAddress?.stateCode)){
            setIsAddressContainerClicked(false)
          }else{
            setIsAddressContainerClicked(true)
          }
        }
      }
    
      // Custom clickaway handler
      useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
          if (!isAddressContainerClicked) return;
          
          const target = event.target as HTMLElement;
          
          // Check if click is inside the address container
          if (addressContainerRef.current && addressContainerRef.current.contains(target)) {
            return;
          }
          
          // Check if click is inside any state selector
          const stateSelectorElement = document.querySelector('[data-state-selector]');
          if (stateSelectorElement && stateSelectorElement.contains(target)) {
            return;
          }
          
          // If we get here, the click was outside both the container and state selector
          handleShipmentAddressContainerClickAway();
        };
    
        // Add event listener when address container is clicked
        if (isAddressContainerClicked) {
          document.addEventListener('mousedown', handleClickOutside);
        }
    
        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
      }, [isAddressContainerClicked]);
    



    console.log("errors", errors ,watch() , isValid , isDirty);

    return (
        <div className={styles.tabContent} ref={selectCompanyHQAddressRef}>
            <div className={styles.formContainer}>
                {/* PARENT COMPANY NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.parentCompanyName && styles.focusLbl)} htmlFor="parentCompanyName">
                            Company Name
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.inputCreateAccount,styles.disableInput)}
                        id='parentCompanyName'
                        tabIndex={0} 
                        onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                parentCompanyName: false,
                            }));
                        }}
                        >{watch('parentCompanyName')}</span>
                    </span>
                </div>

                {/* COMPANY TYPE */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.companyType && styles.focusLbl)} htmlFor="companyType">
                        company type
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <CustomMenu
                            onfocus={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyType: true,
                                }));
                            }}
                            onBlur={() => {
                                setIsInputFocused((prevState) => ({
                                    ...prevState,
                                    companyType: false,
                                }));
                            }}
                            control={control}
                            name={'companyType'}
                            // placeholder={'Company Type'}
                            MenuProps={{
                                classes: {
                                    paper: styles.Dropdownpaper,
                                    list: styles.muiMenuList,
                                    select: styles.selectClassName,
                                },
                                id: 'companyTypeMenu'
                            }}
                            className={styles.selectDropdown}
                            placeholderClass={styles.placeholderTxt}
                            IconComponent={DropdownIcon}
                            items={companyTypes}
                            renderValue={(value: string) => {
                                const selectedType = companyTypes.find(type => type.value === value);
                                return (
                                    <span>
                                        {selectedType?.title}
                                    </span>
                                );
                            }}

                        />
                    </span>
                </div>

                {/* COMPANY HQ ADDRESS */}
                <div className={ clsx(styles.formGroupInput, styles.companyHQAddressContainer)}>
                    <span className={styles.col1}>
                        <label className={clsx((isInputFocused.companyAddressLine1 || isInputFocused.companyAddressLine2 || isInputFocused.companyAddressCity || isInputFocused.companyAddressState || isInputFocused.companyAddressZip) && styles.focusLbl)} htmlFor="companyAddress">
                            Company Address
                        </label>
                    </span>
                    <span className={clsx(styles.col1, styles.locationAddressContainer)}>
                        {
                            isAddressContainerClicked ? (
                            <div className={clsx(styles.customAddressContainer)} ref={addressContainerRef}>
                                <InputWrapper>
                                    <CustomTextField
                                        className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line1 && styles.error)}
                                        type='text'
                                        register={register('companyAddress.line1')}
                                        placeholder='Line 1'
                                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                            register('companyAddress.line1').onBlur(e);
                                            handleInputBlur('companyAddressLine1')
                                        }}
                                        onFocus={() => handleInputFocus('companyAddressLine1')}
                                        errorInput={errors?.companyAddress?.line1}
                                        inputRef={(e: any) => {
                                            line1InputRef.current = e;
                                          }}
                                    />
                                </InputWrapper>
                                <InputWrapper>
                                    <CustomTextField
                                        className={clsx(styles.inputCreateAccount, errors?.companyAddress?.line2 && styles.error)}
                                        type='text'
                                        // autoFocus={true}
                                        register={register('companyAddress.line2')}
                                        placeholder='Line 2'
                                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                            register('companyAddress.line2').onBlur(e);
                                            handleInputBlur('companyAddressLine2')
                                        }}
                                        onFocus={() => handleInputFocus('companyAddressLine2')}
                                        errorInput={errors?.companyAddress?.line2}
                                    />
                                </InputWrapper>

                                <span className={styles.zipInputContainer}>
                                    <span className={styles.col1}>
                                        <InputWrapper>
                                            <CustomTextField
                                                className={clsx(styles.inputCreateAccount, errors?.companyAddress?.city && styles.error)}
                                                type='text'
                                                register={register('companyAddress.city')}
                                                placeholder='City'
                                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                                    register('companyAddress.city').onBlur(e);
                                                    handleInputBlur('companyAddressCity')
                                                }}
                                                onFocus={() => handleInputFocus('companyAddressCity')}
                                                errorInput={errors?.companyAddress?.city}
                                            />
                                        </InputWrapper>
                                    </span>
                                    <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                                        {/* <CustomMenu
                                            control={control}
                                            name={"companyAddress.state"}
                                            placeholder={'State'}
                                            MenuProps={{
                                                classes: {
                                                    paper: clsx(styles.Dropdownpaper, styles.Dropdownpaper1),
                                                    list: styles.muiMenuList,
                                                    select: styles.selectClassName,
                                                },
                                            }}
                                            className={clsx(styles.selectDropdown, styles.selectState)}
                                            items={states.map((x: any) => ({ title: x.code, value: x.id }))}
                                            onChange={(e: any) => {
                                                states.map((item: any) => {
                                                    if (item.id === e.target.value) {
                                                        setValue("companyAddress.stateCode", item.code)
                                                    }
                                                })
                                            }}
                                        /> */}
                                        <Controller
                                            name="companyAddress.state"
                                            control={control}
                                            render={({ field }) => (
                                                <>
                                                <SingleStateSelector
                                                    states={states.map((state: any) => ({ state_code: state.code }))}
                                                    value={field.value}
                                                    onChange={(stateCode) => {
                                                        const selectedState = states.find((state: any) => state.code === stateCode);
                                                        console.log('Selected state object:', selectedState);
                                                        if (selectedState) {
                                                        field.onChange(selectedState.id);
                                                        setValue('companyAddress.stateCode', selectedState.code);
                                                        // Clear any exis ting errors for the state field
                                                        if (errors?.companyAddress?.state) {
                                                            clearErrors('companyAddress.state');
                                                        }
                                                        // Trigger validation after setting the value
                                                        setTimeout(() => {
                                                            trigger('companyAddress.state');
                                                        }, 0);
                                                        } else {
                                                        console.error('State not found for code:', stateCode);
                                                        }
                                                    }}
                                                    onBlur={field.onBlur}
                                                    error={!!errors?.companyAddress?.state}
                                                    placeholder="State"
                                                    stateIdToCode={(stateId) => {
                                                        const state = states.find((s: any) => s.id === stateId);
                                                        return state ? state.code : stateId;
                                                    }}
                                                    onFocusChange={setIsStateSelectorFocused}
                                                    />

                                                    </>
                                            )}
                                        />

                                    </span>
                                    <span className={styles.col3}>
                                        <InputWrapper>
                                            <CustomTextField
                                                className={clsx(styles.inputCreateAccount, (errors?.companyAddress?.zip || errors?.companyAddress?.state) && styles.error)}
                                                type='text'
                                                maxLength={5}
                                                register={register('companyAddress.zip')}
                                                placeholder='Zip Code'
                                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                                    register('companyAddress.zip').onBlur(e);
                                                    handleInputBlur('companyAddressZip');
                                                }}
                                                onFocus={() => handleInputFocus('companyAddressZip')}
                                                errorInput={errors?.companyAddress?.zip || errors?.companyAddress?.state}
                                                mode="wholeNumber"
                                                onKeyDown={(e) => {
                                                    if(e.key === 'Tab'){
                                                        handleShipmentAddressContainerClickAway()
                                                    }
                                                }}
                                            />
                                        </InputWrapper>
                                    </span>
                                </span>

                            </div>
                            ) : (
                                <div className={styles.addressDisplayContainer} onClick={() => setIsAddressContainerClicked(true)}>
                                    {
                                        (watch('companyAddress.line1') || watch('companyAddress.line2') || watch('companyAddress.city') || watch('companyAddress.state') || watch('companyAddress.zip')) ? (
                                            <div className={styles.valueDiv}>
                                                <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('companyAddress.line1') ? `${watch('companyAddress.line1')}` : ''}</p>
                                                <p className={clsx(styles.addressInputs, styles.hideInputBackground)}>{watch('companyAddress.line2') ? `${watch('companyAddress.line2')}` : ''}</p>
                                                <span className={styles.lastAddressFiled}>
                                                    <p className={clsx(styles.addressInputsCol1, styles.hideInputBackground)}>{watch('companyAddress.city') ? `${watch('companyAddress.city')}` : ''}</p>
                                                    <p className={clsx(styles.addressInputsCol2, styles.hideInputBackground)}>{watch('companyAddress.stateCode') ? `${watch('companyAddress.stateCode')}` : ''}</p>
                                                    <p className={clsx(styles.addressInputsCol3, styles.hideInputBackground)}>{watch('companyAddress.zip') ? `${watch('companyAddress.zip')}` : ''}</p>
                                                </span>
                                            </div>
                                        ) : (
                                            <span className={styles.placeHolderDiv} onClick={() => setIsAddressContainerClicked(true)}>Enter Ship-to address</span>
                                        )
                                    }
                                </div>
                            )
                        }
                    </span>
                </div>

                   {/* COMPANY W9 FORM */}
                   <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label htmlFor="companyW9Form">
                        company w-9
                        </label>
                    </span>
                    <span className={styles.col1} >
                        {watch('companyW9Form.cerificate_url_s3') ?
                            <span className={styles.viewBtnContainer}>
                                <a className={styles.viewBtn} href={watch('companyW9Form.cerificate_url_s3')} >{watch('companyW9Form.file_name')}</a><button className={styles.viewBtn} onClick={irsW9FormEditHandler}><IconUpload /> </button>
                            </span>
                            :
                            <label>
                                <button onClick={irsW9FormEditHandler} className={styles.uploadText}>
                                    <span>Upload</span>
                                    <span className={styles.uploadIcon}>
                                        <IconUpload />
                                    </span>
                                </button>
                            </label>
                        }
                        <input className={styles.uploadFileInput} {...register(`companyW9Form.file`)} type='file'  onChange={(e) => { uploadW9Form(e); register(`companyW9Form.file`).onChange(e) }} ref={companyW9FormRef} />
                    </span>
                </div>


                {/* BILLING CONTACT NAME */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.billingContactName && styles.focusLbl)} htmlFor="billingContactName">
                            AP CONTACT NAME
                        </label>
                    </span>
                    <span className={clsx(styles.col1,styles.inputMain)}>
                        <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, errors?.billingContactName && styles.error)}
                                type='text'
                                register={register("billingContactName")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("billingContactName").onBlur(e);
                                    // handleInputBlur('billingContactName')
                                }}
                                onFocus={() => handleInputFocus('billingContactName')}
                                errorInput={errors?.billingContactName}
                            />
                        </InputWrapper>
                    </span>
                </div>

                {/* BILLING CONTACT EMAIL */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.billingContactEmail && styles.focusLbl)} htmlFor="billingContactEmail">
                            AP CONTACT EMAIL
                        </label>
                    </span>
                    <span className={styles.col1}>
                        {/* <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, errors?.billingContactEmail && styles.error)}
                                type='email'
                                register={register("billingContactEmail")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("billingContactEmail").onBlur(e);
                                    handleInputBlur('billingContactEmail')
                                }}
                                onFocus={() => handleInputFocus('billingContactEmail')}
                                errorInput={errors?.billingContactEmail}
                            />
                        </InputWrapper> */}
                          <EmailTagInputField
                            value={watch('billingContactEmail') ? watch('billingContactEmail').split(',').filter(Boolean) : []}
                            onChange={(emails) => {
                                setValue('billingContactEmail', emails.join(','), { 
                                    shouldDirty: true, 
                                    shouldTouch: true 
                                });
                                // Trigger validation after setting the value
                                trigger('billingContactEmail');
                            }}
                            placeholder=""
                            maxEmails={5}
                            register={register("billingContactEmail")}
                            error={errors?.billingContactEmail?.message}
                            onBlur={() => trigger('billingContactEmail')}
                            control={control}
                        />
                    </span>
                </div>

                {/* SEND INVOICES TO */}
                <div className={styles.formGroupInput}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendInvoicesTo && styles.focusLbl)} htmlFor="sendInvoicesTo">
                            SEND INVOICES TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                        {/* <InputWrapper>
                            <CustomTextField
                                className={clsx(styles.inputCreateAccount, styles.sendInvoiceEmailInput,errors?.sendInvoicesTo && styles.error)}
                                type='email'
                                register={register("sendInvoicesTo")}
                                placeholder=''
                                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                    register("sendInvoicesTo").onBlur(e);
                                    handleInputBlur('sendInvoicesTo')
                                }}
                                onFocus={() => handleInputFocus('sendInvoicesTo')}
                                errorInput={errors?.sendInvoicesTo}
                            />
                        </InputWrapper> */}
                        <EmailTagInputField
                            value={watch('sendInvoicesTo') ? watch('sendInvoicesTo').split(',').filter(Boolean) : []}
                            onChange={(emails) => {
                                setValue('sendInvoicesTo', emails.join(','), { 
                                    shouldDirty: true, 
                                    shouldTouch: true 
                                });
                                // Trigger validation after setting the value
                                trigger('sendInvoicesTo');
                            }}
                            placeholder=""
                            maxEmails={5}
                            register={register("sendInvoicesTo")}
                            error={errors?.sendInvoicesTo?.message}
                            onBlur={() => trigger('sendInvoicesTo')}
                            control={control}
                        />

                    </span>
                </div>

                {/* SEND REMITTANCES TO */}
                <div className={clsx(styles.formGroupInput, styles.bdrBtm0)}>
                    <span className={styles.col1}>
                        <label className={clsx(isInputFocused.sendRemittancesTo && styles.focusLbl)} htmlFor="sendRemittancesTo">
                            SEND REMITTANCES TO
                        </label>
                    </span>
                    <span className={styles.col1}>
                        <span className={clsx(styles.inputCreateAccount , styles.arBryzosCom)} tabIndex={0} onFocus={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendRemittancesTo: true,
                            }));
                        }}
                        onBlur={() => {
                            setIsInputFocused((prevState) => ({
                                ...prevState,
                                sendRemittancesTo: false,
                            }));
                        }}
                        onKeyDown={(e) => {
                            if(e.key === 'Tab' && !e.shiftKey){
                                setActiveTab('USER');
                            }
                        }}
                        >
                            {watch('sendRemittancesTo') ?? "<EMAIL>"}
                        </span>
                    </span>
                </div>

            
            </div>
        </div>
    );
};

export default CompanyTab; 
