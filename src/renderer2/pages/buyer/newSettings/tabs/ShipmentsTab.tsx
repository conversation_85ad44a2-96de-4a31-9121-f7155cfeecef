import React, { useEffect, useRef, useState } from 'react';
import styles from './ShipmentTab.module.scss';
import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import CustomToggleCheckbox from 'src/renderer2/component/CustomToggleCheckbox';
import clsx from 'clsx';
import { shipmentsSchema } from '../schemas';
import { BReceivingHoursTooltip, deleteCertificateLineTooltip, deleteCertificateTooltip } from 'src/renderer2/tooltip';
import { Dialog, Fade, Tooltip, Autocomplete, TextField, Select, MenuItem } from '@mui/material';
import { CustomMenu } from '../../CustomMenu';
import axios from 'axios';
import { buyerSettingConst, formatPhoneNumber, formatPhoneNumberRemovingCountryCode, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { commomKeys, defaultResaleCertificateLine, prefixUrl, reactQueryKeys, RecevingHoursFrom, RecevingHoursTo } from 'src/renderer2/common';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import ResaleCertificateLineComponent from '../components/ResaleCertificateLineComponent';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import { ReactComponent as CloseIcon } from '../../../../assets/New-images/New-Image-latest/shipment-popup-close.svg';
import { v4 as uuidv4 } from 'uuid';
import { useQueryClient } from '@tanstack/react-query';
import { unformatPhoneNumber } from 'src/renderer2/helper';
import useSaveUserSettings from 'src/renderer2/hooks/useSaveUserSettings';
import SingleStateSelector from '../components/StateSelector/SingleStateSelector';
import { EmailTagInputField } from 'src/renderer2/component/EmailTagInput';
import usePostDeleteShipment from 'src/renderer2/hooks/usePostDeleteShipment';

interface InputFocusState {
  locationNickName: boolean;
  locationAddressLine1: boolean;
  locationAddressLine2: boolean;
  locationAddressCity: boolean;
  locationAddressState: boolean;
  locationAddressZip: boolean;
  deliveryContactFirstName: boolean;
  deliveryContactLastName: boolean;
  deliveryContactPhoneNumber: boolean;
  deliveryContactEmail: boolean;
  shippingDocsEmail: boolean;
}

const MenuPropsTop = {
  classes: {
    paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown, styles.receivingHoursTop),
    list: styles.muiMenuList
  },
  anchorOrigin: {
    vertical: -5,
    horizontal: "left"
  },
  transformOrigin: {
    vertical: "bottom",
    horizontal: "left"
  },
}
const MenuPropsBottom = {
  classes: {
    paper: clsx(styles.Dropdownpaper, styles.resaleCertdropdown,styles.receivingHoursBottom),
    list: styles.muiMenuList
  },
  anchorOrigin: {
    vertical: 27,
    horizontal: "left"
  },
  transformOrigin: {
    vertical: "top",
    horizontal: "left"
  },
}



const ShipmentsTab = ({selectedShipment, isCreate, closeDialog, onSuccess}: any) => {
  const { userData, showLoader, setShowLoader, referenceData }: any = useGlobalStore();
  const [States, setStates] = useState([]);
  const [ResaleExpiration, setResaleExpiration] = useState([]);
  const { showCommonDialog, resetDialogStore }: any = useDialogStore();
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const shipmentPopupRef = useRef(null);
  const { mutateAsync: saveUserSettings } = useSaveUserSettings();
  const { buyerSetting }: any = useBuyerSettingStore();
  const {
    register,
    handleSubmit,
    clearErrors,
    setError,
    setValue,
    reset,
    watch,
    control,
    getValues,
    trigger,
    resetField,
    formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
    getFieldState,
  } = useForm({
    resolver: yupResolver(shipmentsSchema),
    mode: 'onBlur',
  });
  const isButtonDisabled = !isValid || !isDirty || isSubmitting;
  const { mutateAsync: postDeleteShipment } = usePostDeleteShipment();
  const [isAddressContainerClicked, setIsAddressContainerClicked] = useState(false);
  const stateSelectorRef = useRef<HTMLDivElement>(null);
  const [isStateSelectorFocused, setIsStateSelectorFocused] = useState(false);
  const addressContainerRef = useRef<HTMLDivElement>(null);
  const line1InputRef = useRef<HTMLInputElement>(null);

  // Focus Line 1 input when address container is clicked
  useEffect(() => {
    if (isAddressContainerClicked && line1InputRef.current) {
      // Small delay to ensure the input is rendered
      setTimeout(() => {
        line1InputRef.current?.focus();
      }, 0);
    }
  }, [isAddressContainerClicked]);

  const defaultUserAvailability = [
    {
      "day": "Monday",
      "from": "7",
      "to": "16",
      "display_name": "Mon",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Tuesday",
      "from": "7",
      "to": "16",
      "display_name": "Tue",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Wednesday",
      "from": "7",
      "to": "16",
      "display_name": "Wed",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Thursday",
      "from": "7",
      "to": "16",
      "display_name": "Thu",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    },
    {
      "day": "Friday",
      "from": "7",
      "to": "16",
      "display_name": "Fri",
      "is_user_available": 1,
      "receivingHrsFrom": RecevingHoursFrom,
      "receivingHrsTo": RecevingHoursTo,
    }
  ]

  useEffect(() => {
    if (isCreate) {
      setValue("dates", defaultUserAvailability)
    }else{
      setValue("id", selectedShipment?.id);
      setValue("locationNickName", selectedShipment?.location_nickname);
             setValue('locationAddress', {
         line1: selectedShipment?.line1 || '',
         line2: selectedShipment?.line2 || '',
         city: selectedShipment?.city || '',
         state: selectedShipment?.state_id || null,
         stateCode: selectedShipment?.state_code || null,
         zip: selectedShipment?.zip || '',
       });
      setValue("deliveryApptRequired", Boolean(selectedShipment?.is_appt_required));
      setValue("deliveryContact", {
        firstName: selectedShipment?.first_name,
        lastName: selectedShipment?.last_name,
        phone: selectedShipment?.phone
        ? formatPhoneNumber(
            formatPhoneNumberRemovingCountryCode(selectedShipment?.phone)
          )
        : '',
        email: selectedShipment?.email_id,
      });
      setValue("shippingDocsEmail", selectedShipment?.shipping_docs_to);
      setValue("isDefault", Boolean(selectedShipment?.is_default));
      if (selectedShipment?.user_delivery_receiving_availability_details) {
        const weeks =
          selectedShipment?.user_delivery_receiving_availability_details.map(
            (day: any) => {
              day.receivingHrsFrom = [...RecevingHoursFrom];
              day.receivingHrsTo = [...RecevingHoursTo];
              return day;
            }
          );
        setValue('dates', weeks);
      }
    }
  }, [isCreate, buyerSetting , selectedShipment]);

  
  useEffect(() => {
    if (referenceData) {
      setStates(referenceData.ref_states);
    }
  }, [referenceData]);

  // Remove the useEffect that runs on every change
  // Instead, we'll handle validation in the onBlur events

  const deliveryApptRequired = watch('deliveryApptRequired');

  const [isInputFocused, setIsInputFocused] = useState<any>({
    locationNickName: false,
    locationAddressLine1: false,
    locationAddressLine2: false,
    locationAddressCity: false,
    locationAddressState: false,
    locationAddressZip: false,
    deliveryContactFirstName: false,
    deliveryContactLastName: false,
    deliveryContactPhoneNumber: false,
    deliveryContactEmail: false,
    shippingDocsEmail: false,
  });

  const handleInputFocus = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: true,
    }));
  };

  const handleInputBlur = (inputName: keyof InputFocusState): void => {
    setIsInputFocused((prevState) => ({
      ...prevState,
      [inputName]: false,
    }));
  };

  const changeReceivingHrs = (dateIndex: any, isReceivingHrsFrom: any, dropdownValue: any) => {
    setValue(`dates.${dateIndex}.is_user_available`, true);
    const receivingHrsOption: any[] = [];
    let currentDropdown = `dates.${dateIndex}.to`;
    let adjacentDropdown = `dates.${dateIndex}.from`;
    let adjDropDownOptionsCopy = RecevingHoursFrom;
    let dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsFrom`;
    let onChangingCancelAdjDropDownValue = RecevingHoursFrom[0].value;
    if (isReceivingHrsFrom) {
      currentDropdown = `dates.${dateIndex}.from`;
      adjacentDropdown = `dates.${dateIndex}.to`;
      adjDropDownOptionsCopy = RecevingHoursTo;
      onChangingCancelAdjDropDownValue = RecevingHoursTo[RecevingHoursTo.length - 2].value;
      dropDownOptionsToBeDisabled = `dates.${dateIndex}.receivingHrsTo`;
    }
    setValue(currentDropdown, dropdownValue.toString());
    if (dropdownValue === 'closed') {
      setValue(adjacentDropdown, dropdownValue.toString());
      setValue(`dates.${dateIndex}.is_user_available`, 0);
    }
    else if (watch(adjacentDropdown) === 'closed') {
      setValue(`dates.${dateIndex}.is_user_available`, 1);
      setValue(adjacentDropdown, onChangingCancelAdjDropDownValue.toString());
    }
    adjDropDownOptionsCopy.forEach(timeOption => {
      const time = { ...timeOption };
      if (dropdownValue !== 'closed' && ((!isReceivingHrsFrom && time.value >= dropdownValue) || (isReceivingHrsFrom && time.value <= dropdownValue))) time.disabled = true;
      receivingHrsOption.push(time);
    })
    setValue(dropDownOptionsToBeDisabled, receivingHrsOption);
  }

  useEffect(() => {
    handleStateZipValidation("locationAddress.zip", "locationAddress.state");
  }, [watch('locationAddress.zip'), watch('locationAddress.state')])

  const handleStateZipValidation = async (zipCode: any, stateCode: any) => {
    try {
      if (getValues(zipCode)?.length > 4 && getValues(stateCode)) {
        const payload = {
        data: {
          state_id: getValues(stateCode),
          zip_code: parseInt(getValues(zipCode)),
        },
      };
      const checkStateZipResponse = await axios.post(
        import.meta.env.VITE_API_SERVICE + "/user/checkStateZip",
        payload
      );
      if (checkStateZipResponse.data.data === true) {
        clearErrors([stateCode, zipCode]);
        return true
      } else {
          setError(stateCode, { message: "The zip code and state code do not match" });
          setError(zipCode, { message: "The zip code and state code do not match" });
                return false
        }
      }
    } catch (err) {
      console.error(err)
    }
  };

  const handleSaveShipment = async (data: any) => {
    try {
      setShowLoader(true)
      if (isValid) {
        const isDeliveryAddressPresent = buyerSetting.delivery_addresses && buyerSetting.delivery_addresses.length > 0;
        const payload = {
          address_nickname: data.locationNickName,
          delivery_address: {
            line1: data.locationAddress.line1,
            line2: data.locationAddress.line2 || null,
            city: data.locationAddress.city,
            state_id: Number(data.locationAddress.state),
            zip: data.locationAddress.zip,
          },
          delivery_appt_required: data.deliveryApptRequired,
          delivery_contact_first_name: data.deliveryContact.firstName,
          delivery_contact_last_name: data.deliveryContact.lastName,
          delivery_phone: data.deliveryContact.phone,
          delivery_email_id: data.deliveryContact.email,
          shipping_docs_to: data.shippingDocsEmail,
          user_delivery_receiving_availability_details: data.dates.map((date: any) => {
            const { receivingHrsFrom, receivingHrsTo, ...rest } = date;
            return rest;
          }),
          is_default: isDeliveryAddressPresent ? data.isDefault : true,
        }
        if(data.id &&  !isCreate){
          payload.id = data.id;
        }
        const res =await saveUserSettings({ route: 'user/buyer/settings/shipment', data: payload });
        if(res){
          // queryClient.invalidateQueries()
          if(onSuccess){
            onSuccess()
          }else{
            closeDialog()
          }
        }
      }
    } catch (err) {
      console.error(err)
    } finally {
      setShowLoader(false)
    }
  }

  const handleDeleteShipment = async () => {
    try{
      setShowLoader(true)
      const payload = {
        data: {
          delivery_address_id: watch('id')
        }
      }
      const res = await postDeleteShipment(payload)
      if(res){
        // queryClient.invalidateQueries([reactQueryKeys.getBuyingPreference])
        closeDialog()
      }
    }catch(err){
      console.error(err)
    }finally{
      setShowLoader(false)
    }
  }

  const handleShipmentAddressContainerClickAway = () => {
    if (!isStateSelectorFocused) {
      if(!(errors?.locationAddress?.line1 || errors?.locationAddress?.line2 || errors?.locationAddress?.city || errors?.locationAddress?.state || errors?.locationAddress?.zip || errors?.locationAddress?.stateCode)){
        setIsAddressContainerClicked(false)
      }else{
        setIsAddressContainerClicked(true)
      }
    }
  }

  // Custom clickaway handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!isAddressContainerClicked) return;
      
      const target = event.target as HTMLElement;
      
      // Check if click is inside the address container
      if (addressContainerRef.current && addressContainerRef.current.contains(target)) {
        return;
      }
      
      // Check if click is inside any state selector
      const stateSelectorElement = document.querySelector('[data-state-selector]');
      if (stateSelectorElement && stateSelectorElement.contains(target)) {
        return;
      }
      
      // If we get here, the click was outside both the container and state selector
      handleShipmentAddressContainerClickAway();
    };

    // Add event listener when address container is clicked
    if (isAddressContainerClicked) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isAddressContainerClicked]);


  return (<>
    <div className={styles.shipmentTabContentContainer}>
      <div className={styles.shipmentTabContentHeader}>
        <h2>{isCreate ? "CREATE NEW SHIP-TO" : "EDIT SHIP-TO"}</h2>
        <button onClick={closeDialog}>{isCreate ? "Cancel" : "Cancel Editing"}<CloseIcon /></button>
      </div>
      <div className={styles.shipmentTabContentBody}>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.locationNickName && styles.focusLbl)}>
            Location Nickname
          </div>
          <div className={styles.shipmentTabContentValue}>
            <InputWrapper>
              <CustomTextField
                className={clsx(styles.inputCreateAccount, errors?.locationNickName && styles.error)}
                type='text'
                register={register("locationNickName")}
                placeholder='Example: Houston Yard'
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register("locationNickName").onBlur(e);
                  handleInputBlur('locationNickName')
                }}
                onFocus={() => handleInputFocus('locationNickName')}
                onKeyDown={(e) => {
                  if(e.key === 'Tab'){
                    setIsAddressContainerClicked(true)
                  }
                }}
                errorInput={errors?.locationNickName}
              />
            </InputWrapper>
          </div>
        </div>
        <div className={clsx(styles.shipmentTabContent, styles.companyHQAddressContainer)}>
          <div className={clsx(styles.shipmentTabContentTitle, (isInputFocused.locationAddressLine1 || isInputFocused.locationAddressLine2 || isInputFocused.locationAddressCity || isInputFocused.locationAddressState || isInputFocused.locationAddressZip) && styles.focusLbl)}>
            location address
          </div>
          <div className={clsx(styles.shipmentTabContentValue, styles.locationAddressContainer)}>
              {
                isAddressContainerClicked ? 
                <div className={clsx(styles.customAddressContainer)} ref={addressContainerRef}>
                <InputWrapper>
                  <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.locationAddress?.line1 && styles.error)}
                    type='text'
                    register={register('locationAddress.line1')}
                    placeholder='Line 1'
                    onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                      register('locationAddress.line1').onBlur(e);
                      handleInputBlur('locationAddressLine1')
                    }}
                    onFocus={() => handleInputFocus('locationAddressLine1')}
                    errorInput={errors?.locationAddress?.line1}
                    inputRef={(e: any) => {
                      line1InputRef.current = e;
                    }}

                  />
                </InputWrapper>
                <InputWrapper>
                  <CustomTextField
                    className={clsx(styles.inputCreateAccount, errors?.locationAddress?.line2 && styles.error)}
                    type='text'
                    // autoFocus={true}
                    register={register('locationAddress.line2')}
                    placeholder='Line 2'
                    onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                      register('locationAddress.line2').onBlur(e);
                      handleInputBlur('locationAddressLine2')
                    }}
                    onFocus={() => handleInputFocus('locationAddressLine2')}
                    errorInput={errors?.locationAddress?.line2}
                  />
                </InputWrapper>
  
                <span className={styles.zipInputContainer}>
                  <span className={styles.col1}>
                    <InputWrapper>
                      <CustomTextField
                        className={clsx(styles.inputCreateAccount, errors?.locationAddress?.city && styles.error)}
                        type='text'
                        register={register('locationAddress.city')}
                        placeholder='City'
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          register('locationAddress.city').onBlur(e);
                          handleInputBlur('locationAddressCity')
                        }}
                        onFocus={() => handleInputFocus('locationAddressCity')}
                        errorInput={errors?.locationAddress?.city}
                      />
                    </InputWrapper>
                  </span>
                  <span className={clsx(styles.inputSection, styles.yourLocationAdressState, styles.col2, styles.bdrRadius0, styles.bdrRight0)}>
                    <Controller
                      name="locationAddress.state"
                      control={control}
                      render={({ field }) => (
                        <div data-state-selector>
                          <SingleStateSelector
                            states={States.map((state: any) => ({ state_code: state.code }))}
                            value={field.value}
                            onChange={(stateCode) => {
                              console.log('State selected:', stateCode);
                              console.log('Available states:', States);
                              const selectedState = States.find((state: any) => state.code === stateCode);
                              console.log('Selected state object:', selectedState);
                              if (selectedState) {
                                field.onChange(selectedState.id);
                                setValue('locationAddress.stateCode', selectedState.code);
                                // Clear any existing errors for the state field
                                if (errors?.locationAddress?.state) {
                                  clearErrors('locationAddress.state');
                                }
                                // Trigger validation after setting the value
                                setTimeout(() => {
                                  trigger('locationAddress.state');
                                }, 0);
                              } else {
                                console.error('State not found for code:', stateCode);
                              }
                            }}
                            onBlur={field.onBlur}
                            error={!!errors?.locationAddress?.state}
                            placeholder="State"
                            stateIdToCode={(stateId) => {
                              const state = States.find((s: any) => s.id === stateId);
                              return state ? state.code : stateId;
                            }}
                            onFocusChange={setIsStateSelectorFocused}
                          />
                        </div>
                      )}
                    />
                  </span>
                  <span className={styles.col3}>
                    <InputWrapper>
                      <CustomTextField
                        className={clsx(styles.inputCreateAccount, (errors?.locationAddress?.zip || errors?.locationAddress?.state) && styles.error)}
                        type='text'
                        maxLength={5}
                        register={register('locationAddress.zip')}
                        placeholder='Zip Code'
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          register('locationAddress.zip').onBlur(e);
                          handleInputBlur('locationAddressZip');
                        }}
                        onFocus={() => handleInputFocus('locationAddressZip')}
                        errorInput={errors?.locationAddress?.zip || errors?.locationAddress?.state}
                        mode="wholeNumber"
                        onKeyDown={(e) => {
                          if(e.key === 'Tab'){
                            handleShipmentAddressContainerClickAway()
                          }
                        }}
                      />
                    </InputWrapper>
                  </span>
                </span>
  
              </div> : <div className={styles.addressDisplayContainer} onClick={() => setIsAddressContainerClicked(true)}> 
                {
                  (watch('locationAddress.line1') || watch('locationAddress.line2') || watch('locationAddress.city') || watch('locationAddress.state') || watch('locationAddress.zip')) ? (
                    <div className={styles.valueDiv}>
                      <p className={clsx(styles.addressInputs,styles.hideInputBackground)}>{watch('locationAddress.line1') ? `${watch('locationAddress.line1')}` : ''}</p>
                      <p className={clsx(styles.addressInputs,styles.hideInputBackground)}>{watch('locationAddress.line2') ? `${watch('locationAddress.line2')}` : ''}</p>
                      <span className={styles.lastAddressFiled}>
                        <p className={clsx(styles.addressInputsCol1,styles.hideInputBackground)}>{watch('locationAddress.city') ? `${watch('locationAddress.city')}` : ''}</p>
                        <p className={clsx(styles.addressInputsCol2,styles.hideInputBackground)}>{watch('locationAddress.stateCode') ? `${watch('locationAddress.stateCode')}` : ''}</p>
                        <p className={clsx(styles.addressInputsCol3,styles.hideInputBackground)}>{watch('locationAddress.zip') ? `${watch('locationAddress.zip')}` : ''}</p>
                      </span>
                    </div>
                  ) : (
                    <span className={styles.placeHolderDiv} onClick={() => setIsAddressContainerClicked(true)}>Enter Ship-to address</span>
                  )
                }
              </div>
              }
            </div>
          </div>
        <div className={clsx(styles.shipmentTabContent, styles.receivingHoursInput)}>
          <div className={clsx(styles.shipmentTabContentTitle)} htmlFor="deliveryApptRequired">
            RECEIVING HOURS
          </div>
          <div className={styles.shipmentTabContentValue}>
            {watch('dates')?.map((x: any, i: any) => (<span key={x.day} className={styles.inputSectionRecevingHours}>
              <span className={`${watch(`dates.${i}.from`) !== 'closed' ? styles.daylbl1 : styles.daylbl1}`}>{x.display_name}</span>
              <span className={clsx(styles.daylbl2, 'w100 dflex')}>
                <CustomMenu
                  control={control}
                  defaultValue={x.from}
                  name={`dates.${i}.from`}
                  // className={'selectReceivingHours selectUploadCertDropdown'}
                  className={clsx((!dirtyFields.dates?.[i]?.from && 'disabledDropdown'), (x.from === 'closed' && 'txtClosed'), 'selectReceivingHours selectUploadCertDropdown')}
                  MenuProps={MenuPropsTop}
                  items={x.receivingHrsFrom}
                  IconComponent={DropdownIcon}
                  onChange={(events: any) => {
                    changeReceivingHrs(i, true, events.target.value);
                  }}
                />
              </span>
              <span className={clsx(styles.daylbl3, 'w100 dflex')}>
                <CustomMenu
                  defaultValue={x.to}
                  control={control}
                  name={`dates.${i}.to`}
                  className={clsx((!dirtyFields.dates?.[i]?.to && 'disabledDropdown'), (x.to === 'closed' && 'txtClosed'), 'selectReceivingHours selectUploadCertDropdown')}
                  MenuProps={MenuPropsBottom}
                  IconComponent={DropdownIcon}
                  items={x.receivingHrsTo}
                  onChange={(events: any) => {
                    changeReceivingHrs(i, false, events.target.value);
                  }}
                />
              </span>
            </span>))}
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle)} htmlFor="deliveryApptRequired">
            DELIVERY APPT REQUIRED?
          </div>
          <div className={clsx(styles.shipmentTabContentValue, styles.deliveryApptRequiredContainer)}>
            <Select
              value={watch('deliveryApptRequired') || false}
              onChange={(event: any) => {
                setValue('deliveryApptRequired', event.target.value)
              }}
              className={clsx('selectDropdown', styles.dropdownValue)}
              MenuProps={
                {
                  classes: {
                    paper: styles.dropDownBG
                  },
                }
              }
            >
              <MenuItem value={true}>Yes</MenuItem>
              <MenuItem value={false}>No</MenuItem>
            </Select>
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle, (isInputFocused.deliveryContactFirstName || isInputFocused.deliveryContactLastName) && styles.focusLbl)} htmlFor="deliveryContactFirstName">
            DELIVERY CONTACT NAME
          </div>
          <div className={styles.shipmentTabContentValue}>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.deliveryContact?.firstName && styles.error
                )}
                id='deliveryContactFirstName'
                type='text'
                register={register('deliveryContact.firstName')}
                placeholder='FIRST NAME'
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('deliveryContact.firstName').onBlur(e);
                  handleInputBlur('deliveryContactFirstName');
                }}
                onFocus={() => handleInputFocus('deliveryContactFirstName')}
                errorInput={errors?.deliveryContact?.firstName}
              // onKeyDown={(e) => {
              //   if(e.key === 'Tab'){
              //     if(e.shiftKey){
              //       setActiveTab('COMPANY');
              //     }
              //   }
              // }}
              />
            </InputWrapper>
            <InputWrapper>
              <CustomTextField
                className={clsx(
                  styles.inputCreateAccount,
                  errors?.deliveryContact?.lastName && styles.error
                )}
                id='deliveryContactLastName'
                type='text'
                register={register('deliveryContact.lastName')}
                placeholder='LAST NAME'
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register('deliveryContact.lastName').onBlur(e);
                  handleInputBlur('deliveryContactLastName');
                }}
                onFocus={() => handleInputFocus('deliveryContactLastName')}
                errorInput={errors?.deliveryContact?.lastName}
              // onKeyDown={(e) => {
              //   if(e.key === 'Tab'){
              //     if(e.shiftKey){
              //       setActiveTab('COMPANY');
              //     }
              //   }
              // }}
              />
            </InputWrapper>
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.deliveryContactPhoneNumber && styles.focusLbl)} htmlFor="deliveryPhoneNumber">
            DELIVERY PHONE NUMBER
          </div>
          <div className={styles.shipmentTabContentValue}>
            <InputWrapper>
              <CustomTextField
                tabIndex={watch('deliveryApptRequired') ? 0 : -1}
                className={clsx(styles.inputCreateAccount, errors?.deliveryContact?.phone && styles.error)}
                type='tel'
                register={register("deliveryContact.phone")}
                onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                  register("deliveryContact.phone").onBlur(e);
                  handleInputBlur('deliveryContactPhoneNumber')
                }}
                onFocus={() => handleInputFocus('deliveryContactPhoneNumber')}
                errorInput={errors?.deliveryContact?.phone}
                mode="phoneNumber"
                placeholder='(XXX) XXX-XXXX'
              />
            </InputWrapper>
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.deliveryContactEmail && styles.focusLbl)} htmlFor="deliveryEmailAddress">
            DELIVERY EMAIL ADDRESS
          </div>
          <div className={styles.shipmentTabContentValue}>
            <EmailTagInputField
              value={watch('deliveryContact.email') ? watch('deliveryContact.email').split(',').filter(Boolean) : []}
              onChange={(emails) => {
                setValue('deliveryContact.email', emails.join(','), { 
                  shouldDirty: true, 
                  shouldTouch: true 
                });
                // Trigger validation after setting the value
                trigger('deliveryContact.email');
              }}
              placeholder=""
              maxEmails={5}
              register={register("deliveryContact.email")}
              error={errors?.deliveryContact?.email}
              onBlur={() => trigger('deliveryContact.email')}
              control={control}
            />
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle, isInputFocused.shippingDocsEmail && styles.focusLbl)}>
            Email shipping docs to
          </div>
          <div className={styles.shipmentTabContentValue}>
            <EmailTagInputField
              value={watch('shippingDocsEmail') ? watch('shippingDocsEmail').split(',').filter(Boolean) : []}
              onChange={(emails) => {
                setValue('shippingDocsEmail', emails.join(','), { 
                  shouldDirty: true, 
                  shouldTouch: true 
                });
                // Trigger validation after setting the value
                trigger('shippingDocsEmail');
              }}
              placeholder=""
              maxEmails={5}
              register={register("shippingDocsEmail")}
              error={errors?.shippingDocsEmail}
              onBlur={() => trigger('shippingDocsEmail')}
              control={control}
            />
          </div>
        </div>
        <div className={styles.shipmentTabContent}>
          <div className={clsx(styles.shipmentTabContentTitle)} htmlFor="isDefault">
            Set as default
          </div>
          <div className={styles.shipmentTabContentValue}>
            <CustomToggleCheckbox
              name="isDefault"
              control={control}
              onChange={
                (e: any) => {
                  setValue('isDefault', e);
                }
              }
            />
          </div>
        </div>
        </div>
        <div className={styles.footerContainer}>
          {
            !isCreate && (
                <button className={styles.deleteBtn} onClick={() => setOpenDeleteConfirmation(true)}>Delete Ship-To</button>
            )
          }
          <button className={styles.saveBtn} onClick={() => handleSubmit(handleSaveShipment)()} disabled={isButtonDisabled} >Save</button>
        </div>
        <Dialog
          open={openDeleteConfirmation}
          transitionDuration={200}
          hideBackdrop
          classes={{
            root: styles.ErrorDialog,
            paper: styles.dialogContent
          }}
          container={shipmentPopupRef.current}
          style={{
            position: 'absolute',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: showLoader ? 0 : 1
          }}
          PaperProps={{
            style: {
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              margin: 0,
              backgroundColor: 'transparent'
            }
          }}
        >
          <div className={styles.deleteDialogContainer}>
            <p className={styles.deleteDialogTitle}>Are you sure you want to delete ?</p>
            <div className={styles.deleteBtnSection}>
              <button className={styles.submitYesBtn} onClick={handleDeleteShipment}>Yes</button>
              <button className={styles.submitNoBtn} onClick={() => setOpenDeleteConfirmation(false)}>No</button>
            </div>
          </div>
        </Dialog>

      </div>
    </>
    );
};

    export default ShipmentsTab;


