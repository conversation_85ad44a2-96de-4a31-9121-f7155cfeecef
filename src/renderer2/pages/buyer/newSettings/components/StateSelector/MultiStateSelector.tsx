import React, { useState, useEffect, useRef } from 'react';
import styles from './InteractiveStateSelector.module.scss';
import clsx from 'clsx';
import { Popover } from '@mui/material';
import { ReactComponent as Shape1 } from '../../../../../assets/New-images/New-Image-latest/state-select-multi1.svg';
import { ReactComponent as Shape2 } from '../../../../../assets/New-images/New-Image-latest/state-select-multi-right.svg';

import { ReactComponent as DropdownIcon } from '../../../../../assets/New-images/StateIconDropDpown.svg';
interface State {
  state_code: string;
}

interface MultiStateSelectorProps {
  states: State[];
  value?: string[];
  onChange: (stateCodes: string[]) => void;
  onBlur?: () => void;
  error?: boolean;
  placeholder?: string;
}

const MultiStateSelector: React.FC<MultiStateSelectorProps> = ({
  states,
  value = [],
  onChange,
  onBlur,
  error,
  placeholder = "Type to filter states..."
}) => {
  const [filterText, setFilterText] = useState('');
  const [filteredStates, setFilteredStates] = useState<State[]>(states);
  const [isFocused, setIsFocused] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  // Show selected states in input when closed, filter text when open
  const inputValue = isFocused ? filterText : (value.length > 0 ? value.join(', ') : '');

  // Always show all states, filtering is handled by styling
  useEffect(() => {
    setFilteredStates(states);
  }, [states]);

  // Get navigable states (either all states or filtered states based on input)
  const getNavigableStates = () => {
    if (!filterText.trim()) {
      return states; // Include all states, including selected ones
    }
    return states.filter(state =>
      state.state_code.toLowerCase().startsWith(filterText.toLowerCase())
    );
  };

  // Get the current navigable states
  const navigableStates = getNavigableStates();

  // Get styling class for each state based on filter and selection
  const getStateClass = (stateCode: string, index: number) => {
    const isSelected = value.includes(stateCode);
    const isExactMatch = filterText && stateCode.toLowerCase() === filterText.toLowerCase();
    const isStartsWithMatch = filterText && stateCode.toLowerCase().startsWith(filterText.toLowerCase());
    
    // Check if this state is hovered by finding its index in navigableStates
    const navigableIndex = navigableStates.findIndex(state => state.state_code === stateCode);
    const isHovered = navigableIndex === hoveredIndex && navigableIndex >= 0; // Only hover if state is navigable
    
    return clsx(
      styles.stateItem,
      isSelected && styles.selected,
      isHovered && styles.hovered,
      !filterText && styles.default, // grey by default when no filter
      isExactMatch && styles.exactMatch, // blue for exact match
      isStartsWithMatch && !isExactMatch && styles.startsWithMatch, // white for startsWith matches
      filterText && !isStartsWithMatch && styles.noMatch // grey for non-matches when filtering
    );
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilterText(e.target.value);
    setHoveredIndex(-1); // Reset hover when filter changes
  };

  // Helper function to move focus to next input
  const moveToNextInput = () => {
    // Find all focusable elements in the document
    const focusableElements = document.querySelectorAll(
      'input, select, textarea, button, [tabindex]:not([tabindex="-1"])'
    );
    
    // Find the current input's index
    const currentIndex = Array.from(focusableElements).findIndex(el => el === inputRef.current);
    
    if (currentIndex !== -1 && currentIndex < focusableElements.length - 1) {
      // Focus the next element
      const nextElement = focusableElements[currentIndex + 1] as HTMLElement;
      if (nextElement && nextElement.focus) {
        nextElement.focus();
      }
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === 'Tab') {
      e.preventDefault();
      
      // If there's a hovered state, select it
      if (hoveredIndex >= 0 && hoveredIndex < navigableStates.length) {
        const newStateCode = navigableStates[hoveredIndex].state_code;
        if (value.includes(newStateCode)) {
          // If already selected, unselect it
          const newValue = value.filter(code => code !== newStateCode);
          onChange(newValue);
        } else {
          // If not selected, add it
          const newValue = [...value, newStateCode];
          onChange(newValue);
        }
        setFilterText(''); // Clear input after selection
        setHoveredIndex(-1);
        setIsFocused(false); // Close dropdown after selection
        // Move to next input instead of just blurring
        setTimeout(() => moveToNextInput(), 0);
        onBlur?.();
        return;
      }
      
      // Find exact matches from navigable states
      const exactMatches = navigableStates.filter(state =>
        state.state_code.toLowerCase() === filterText.toLowerCase()
      );
      
      // If only one exact match exists, select it
      if (exactMatches.length === 1) {
        const newStateCode = exactMatches[0].state_code;
        if (value.includes(newStateCode)) {
          // If already selected, unselect it
          const newValue = value.filter(code => code !== newStateCode);
          onChange(newValue);
        } else {
          // If not selected, add it
          const newValue = [...value, newStateCode];
          onChange(newValue);
        }
        setFilterText(''); // Clear input after selection
        setHoveredIndex(-1);
        setIsFocused(false); // Close dropdown after selection
        // Move to next input instead of just blurring
        setTimeout(() => moveToNextInput(), 0);
        onBlur?.();
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (navigableStates.length > 0) {
        const newIndex = hoveredIndex < navigableStates.length - 1 ? hoveredIndex + 1 : 0;
        setHoveredIndex(newIndex);
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (navigableStates.length > 0) {
        const newIndex = hoveredIndex > 0 ? hoveredIndex - 1 : navigableStates.length - 1;
        setHoveredIndex(newIndex);
      }
    } else if (e.key === 'Backspace') {
      // Remove last selected state on backspace if input is empty
      if (filterText === '' && value.length > 0) {
        const newValue = value.slice(0, -1);
        onChange(newValue);
      }
    }
  };

  const handleStateClick = (stateCode: string) => {
    if (value.includes(stateCode)) {
      // If already selected, unselect it
      const newValue = value.filter(code => code !== stateCode);
      onChange(newValue);
      setFilterText(''); // Clear input after selection
      setHoveredIndex(-1);
    } else {
      // If not selected, add it
      const newValue = [...value, stateCode];
      onChange(newValue);
      setFilterText(''); // Clear input after selection
      setHoveredIndex(-1);
    }
    // Close dropdown and remove focus when state is selected
    setIsFocused(false);
    // Move to next input instead of just blurring
    setTimeout(() => moveToNextInput(), 0);
    onBlur?.();
  };

  const handleStateMouseEnter = (stateCode: string) => {
    const navigableIndex = navigableStates.findIndex(state => state.state_code === stateCode);
    // Only set hover if the state is navigable
    if (navigableIndex >= 0) {
      setHoveredIndex(navigableIndex);
    }
  };

  const handleStateMouseLeave = () => {
    setHoveredIndex(-1);
  };

  const handleInputFocus = () => {
    setIsFocused(true);
    setFilterText(''); // Clear input when opening dropdown for searching
  };

  const handleInputBlur = () => {
    // Close dropdown immediately when input loses focus
    setIsFocused(false);
    setFilterText(''); // Clear filter text when closing
    setHoveredIndex(-1);
    onBlur?.();
  };

  const handlePopoverClose = () => {
    setIsFocused(false);
    setFilterText('');
    setHoveredIndex(-1);
    onBlur?.();
  };
  return (
    <div className={clsx(styles.MultiStateSelectDropdown, isFocused && styles.selectShade)}>
        {isFocused && <>
              <div className={styles.shape1}>
                <Shape1/>
              </div>
              <div className={styles.shape2}>
                <Shape2/>
              </div>
            </>
      
            }
      <div className={clsx(styles.inputContainer,styles.multiInputContainer, isFocused && styles.stateWrapper)}>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={value.length > 0 ? '' : placeholder}
          className={clsx(
            styles.input,
            error && styles.inputError,
            isFocused && styles.inputFocused
          )}
        />
          {isFocused && <DropdownIcon />}
      </div>
      
       <Popover
        open={isFocused}
        anchorEl={inputRef.current}
        onClose={handlePopoverClose}
        classes={{
          paper: styles.popperPaperMulti,
        }}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 250,
        }}
        disableAutoFocus
        disablePortal
        disableEnforceFocus
      >
        <div className={clsx(styles.statesGrid,styles.statesGrid1)}>
            {filteredStates.map((state, index) => (
            <button
              type="button"
              key={state.state_code}
              className={getStateClass(state.state_code, index)}
              onClick={() => handleStateClick(state.state_code)}
              onMouseEnter={() => handleStateMouseEnter(state.state_code)}
              onMouseLeave={handleStateMouseLeave}
            >
              {state.state_code}
            </button>
          ))}
        </div>
      </Popover>
    </div>
  );
};

export default MultiStateSelector; 