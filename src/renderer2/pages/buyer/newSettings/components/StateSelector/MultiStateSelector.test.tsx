import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import MultiStateSelector from './MultiStateSelector';

// Mock the SVG imports
jest.mock('../../../../../assets/New-images/New-Image-latest/state-select-multi1.svg', () => ({
  ReactComponent: () => <div data-testid="shape1" />
}));

jest.mock('../../../../../assets/New-images/New-Image-latest/state-select-multi-right.svg', () => ({
  ReactComponent: () => <div data-testid="shape2" />
}));

jest.mock('../../../../../assets/New-images/StateIconDropDpown.svg', () => ({
  ReactComponent: () => <div data-testid="dropdown-icon" />
}));

// Mock the moveToNextInput function
const mockMoveToNextInput = jest.fn();
jest.mock('../../../../../../helper', () => ({
  moveToNextInput: () => mockMoveToNextInput()
}));

const mockStates = [
  { state_code: 'CA' },
  { state_code: 'NY' },
  { state_code: 'TX' },
  { state_code: 'FL' }
];

describe('MultiStateSelector', () => {
  const defaultProps = {
    states: mockStates,
    value: [],
    onChange: jest.fn(),
    onBlur: jest.fn(),
    error: false,
    placeholder: 'Type to filter states...'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<MultiStateSelector {...defaultProps} />);
    expect(screen.getByPlaceholderText('Type to filter states...')).toBeInTheDocument();
  });

  it('opens dropdown when input is focused', async () => {
    render(<MultiStateSelector {...defaultProps} />);
    const input = screen.getByPlaceholderText('Type to filter states...');
    
    fireEvent.focus(input);
    
    await waitFor(() => {
      expect(screen.getByText('CA')).toBeInTheDocument();
      expect(screen.getByText('NY')).toBeInTheDocument();
      expect(screen.getByText('TX')).toBeInTheDocument();
      expect(screen.getByText('FL')).toBeInTheDocument();
    });
  });

  it('selects state when clicked and calls onChange', async () => {
    const mockOnChange = jest.fn();
    render(<MultiStateSelector {...defaultProps} onChange={mockOnChange} />);
    
    const input = screen.getByPlaceholderText('Type to filter states...');
    fireEvent.focus(input);
    
    await waitFor(() => {
      expect(screen.getByText('CA')).toBeInTheDocument();
    });
    
    const caButton = screen.getByText('CA');
    fireEvent.mouseDown(caButton);
    
    expect(mockOnChange).toHaveBeenCalledWith(['CA']);
  });

  it('handles multiple state selection', async () => {
    const mockOnChange = jest.fn();
    render(<MultiStateSelector {...defaultProps} onChange={mockOnChange} value={['CA']} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.focus(input);
    
    await waitFor(() => {
      expect(screen.getByText('NY')).toBeInTheDocument();
    });
    
    const nyButton = screen.getByText('NY');
    fireEvent.mouseDown(nyButton);
    
    expect(mockOnChange).toHaveBeenCalledWith(['CA', 'NY']);
  });

  it('unselects state when already selected state is clicked', async () => {
    const mockOnChange = jest.fn();
    render(<MultiStateSelector {...defaultProps} onChange={mockOnChange} value={['CA', 'NY']} />);
    
    const input = screen.getByRole('textbox');
    fireEvent.focus(input);
    
    await waitFor(() => {
      expect(screen.getByText('CA')).toBeInTheDocument();
    });
    
    const caButton = screen.getByText('CA');
    fireEvent.mouseDown(caButton);
    
    expect(mockOnChange).toHaveBeenCalledWith(['NY']);
  });

  it('prevents input blur when mousedown on state button', async () => {
    const mockOnBlur = jest.fn();
    render(<MultiStateSelector {...defaultProps} onBlur={mockOnBlur} />);
    
    const input = screen.getByPlaceholderText('Type to filter states...');
    fireEvent.focus(input);
    
    await waitFor(() => {
      expect(screen.getByText('CA')).toBeInTheDocument();
    });
    
    const caButton = screen.getByText('CA');
    
    // Simulate mousedown event which should prevent default and handle selection
    const mouseDownEvent = new MouseEvent('mousedown', { bubbles: true });
    Object.defineProperty(mouseDownEvent, 'preventDefault', {
      value: jest.fn(),
      writable: true
    });
    
    fireEvent(caButton, mouseDownEvent);
    
    // The preventDefault should have been called to prevent input blur
    expect(mouseDownEvent.preventDefault).toHaveBeenCalled();
  });

  it('displays selected states in input when closed', () => {
    render(<MultiStateSelector {...defaultProps} value={['CA', 'NY']} />);
    
    const input = screen.getByRole('textbox') as HTMLInputElement;
    expect(input.value).toBe('CA, NY');
  });

  it('shows filter text when focused and typing', async () => {
    render(<MultiStateSelector {...defaultProps} />);
    
    const input = screen.getByPlaceholderText('Type to filter states...');
    fireEvent.focus(input);
    
    await userEvent.type(input, 'C');
    
    expect((input as HTMLInputElement).value).toBe('C');
  });
});
