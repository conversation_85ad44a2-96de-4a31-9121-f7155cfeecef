{
  "branch": "STAGING",
  "webpage": "http://localhost:3200",
  "updateUrl": "https://extended-widget-ui-hazel.vercel.app",
  "internetCheckURL": "https://bryzoswidget.com/widget-service/reference-data/homepage",
  "referenceProductPricingDomain": "https://bryzoswidget.com/widget-service",
  "pusherNotification": {
    "pusher": {
      "key": "3178f9807b7280d4d141",
      "cluster": "ap2"
    },
    "channels": [
      "extended-pricing-widget-notification-staging",
      "private-channel-staging-",
      "extended-pricing-widget-buyer-notification-staging",
      "extended-pricing-widget-seller-notification-staging"
    ],
    "authUrl": "https://bryzoswidget.com/notification-service",
    "channelEvents": {
      "privateEvents": [
        "NOTIFICATION_BUYER_CHECKOUT",
        "NOTIFICATION_SELLER_CHECKOUT",
        "NOTIFICATION_BNPL_APPROVAL_STATUS",
        "NOTIFICATION_BUYER_INVOICE_READY",
        "NOTIFICATION_SELLER_FUNDING_INITIATED",
        "NOTIFICATION_BUYER_SALES_TAX_REMINDER",
        "NOTIFICATION_SELLER_ORDER_PREVIEW",
        "NOTIFICATION_SELLER_ORDER_CLAIM",
        "NOTIFICATION_BUYER_ORDER_CANCEL",
        "NOTIFICATION_BUYER_ORDER_LINE_CANCEL",
        "NOTIFICATION_SELLER_ORDER_CANCEL",
        "NOTIFICATION_SELLER_ORDER_LINE_CANCEL",
        "NOTIFICATION_BUYER_NEVER_PURCHASED",
        "NOTIFICATION_SELLER_NEVER_CLAIMED",
        "CUSTOM_NOTIFICATION",
        "NOTIFICATION_RECEIVED",
        "NOTIFICATION_PRICE_CHANGES",
        "NOTIFICATION_PRODUCT_CHANGES"
      ],
      "publicEvents": [
        "NOTIFICATION_PUBLIC",
        "CUSTOM_NOTIFICATION",
        "NOTIFICATION_RECEIVED",
        "NOTIFICATION_PRICE_CHANGES",
        "NOTIFICATION_PRODUCT_CHANGES"
      ],
      "buyerEvents": [
        "CUSTOM_NOTIFICATION",
        "NOTIFICATION_RECEIVED",
        "NOTIFICATION_PRICE_CHANGES",
        "NOTIFICATION_PRODUCT_CHANGES"
      ],
      "sellerEvents": [
        "NOTIFICATION_SELLER_ORDER_PREVIEW",
        "NOTIFICATION_SELLER_ORDER_CLAIM",
        "NOTIFICATION_SELLER_NEW_BUYER_ADDED",
        "CUSTOM_NOTIFICATION",
        "NOTIFICATION_RECEIVED",
        "NOTIFICATION_PRICE_CHANGES",
        "NOTIFICATION_PRODUCT_CHANGES"
      ]
    }
  },
  "commonAppEventsofPusher": {
    "privateEvents": {
      "customNotification": "CUSTOM_NOTIFICATION",
      "userUiUploadLog":"USER_UI_UPLOAD_LOG",
    },
    "publicEvents": {
      "customNotification": "CUSTOM_NOTIFICATION"
    },
    "buyerEvents": {
      "customNotification": "CUSTOM_NOTIFICATION"
    },
    "sellerEvents": {
      "customNotification": "CUSTOM_NOTIFICATION"
    }
  }
}